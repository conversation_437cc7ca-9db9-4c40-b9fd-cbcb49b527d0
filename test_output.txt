Testing improved error handling...
Traceback (most recent call last):
  File "/Users/<USER>/IdeaProjects/kbot-load-scheduler/test_error_handling.py", line 76, in <module>
    test_improved_error_handling()
  File "/Users/<USER>/IdeaProjects/kbot-load-scheduler/test_error_handling.py", line 29, in test_improved_error_handling
    config = ConfluenceConfig.from_dict(config_dict)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: type object 'ConfluenceConfig' has no attribute 'from_dict'
