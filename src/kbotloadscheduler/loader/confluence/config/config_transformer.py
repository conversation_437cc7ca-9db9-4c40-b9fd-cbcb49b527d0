"""
Configuration Transformer for Confluence Sources
================================================

This module handles Confluence-specific configuration transformations.
"""

from typing import Dict, Any, List
from kbotloadscheduler.bean.beans import SourceBean


class ConfluenceConfigTransformer:
    """
    Handles Confluence-specific configuration transformations.

    This class encapsulates all the logic for transforming raw configuration
    into a Confluence-compatible format.
    """

    @staticmethod
    def _parse_comma_separated_string(items_str: Any) -> List[str]:
        """
        Parses a comma-separated string into a list of cleaned strings.
        Returns an empty list if the input is not a string or is empty.
        """
        if not items_str or not isinstance(items_str, str):
            return []
        # No need for a try-except block here, the initial check is sufficient.
        return [item.strip() for item in items_str.split(",") if item.strip()]

    @classmethod
    def transform_configuration(cls, source: SourceBean) -> Dict[str, Any]:
        """
        Transform a SourceBean configuration for Confluence compatibility.

        This method only applies transformations for Confluence sources
        (where src_type == "confluence"). For other source types, it returns
        the raw configuration unchanged.

        Transformations applied for Confluence sources:
        - "space_key": "VALUE" -> "spaces": ["VALUE"]
        - Converts comma-separated string fields ("labels", "exclude_labels",
          "file_extensions") into lists of strings.
        - Sets default values for "include_content_in_metadata" and
          "duplicate_filename_strategy" if they are not already present.

        Args:
            source: A SourceBean instance containing the source configuration.

        Returns:
            A transformed configuration dictionary ready for the Confluence loader.
        """
        config = source.parse_configuration()

        if source.src_type != "confluence":
            return config

        # Handle backward compatibility for space_key -> spaces
        if "space_key" in config:
            space_key = config.pop("space_key")
            # Only set 'spaces' if it doesn't already exist and space_key has a value.
            if "spaces" not in config and space_key:
                config["spaces"] = [space_key]

        # Transform any comma-separated string fields to lists.
        string_to_list_keys = ["labels", "exclude_labels", "file_extensions"]
        for key in string_to_list_keys:
            if key in config and isinstance(config[key], str):
                config[key] = cls._parse_comma_separated_string(config[key])

        # Set sensible defaults if not specified.
        # setdefault is a clean way to add a key only if it's not already present.
        config.setdefault("include_content_in_metadata", False)
        config.setdefault("duplicate_filename_strategy", "append_id")

        return config
