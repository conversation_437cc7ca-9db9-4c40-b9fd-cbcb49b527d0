import logging
import os
import xml.etree.ElementTree as ET
from contextlib import contextmanager
from functools import lru_cache
from typing import Optional, List, Dict, Tuple, Set

import requests
from atlassian import Confluence

from kbotloadscheduler.exceptions import (
    ConfluenceClientException,
    ConfluenceAuthenticationError,  # noqa: F401
    ConfluenceNotFoundError,        # noqa: F401
    ConfluenceTimeoutError,
)
from ..config.confluence_config import ConfluenceConfig
from .confluence_credentials import ConfluenceCredentials
from ..utils.retry_factory import create_api_retryer, create_search_retryer

logger = logging.getLogger(__name__)

# ------------------------------------------------------------------
# Caching & Network Helpers
# ------------------------------------------------------------------

@lru_cache(maxsize=512)
def _cached_fetch_attachment(url: str, attachment_id: str, verify_ssl: bool) -> Dict:
    api_url = f"{url}/rest/api/content/{attachment_id}"
    with requests.Session() as s:
        s.verify = verify_ssl
        headers = {
            "Accept": "application/json",
            "User-Agent": "Mozilla/5.0 (compatible; KbotConfluenceClient/1.3)"
        }
        resp = s.get(api_url, headers=headers, timeout=30)
        resp.raise_for_status()
        return resp.json()

# ------------------------------------------------------------------
# Main Client
# ------------------------------------------------------------------

class ConfluenceClient:
    def __init__(self, credentials: ConfluenceCredentials, config: ConfluenceConfig):
        if not isinstance(credentials, ConfluenceCredentials) or not credentials.is_valid():
            raise ConfluenceAuthenticationError("A valid ConfluenceCredentials object is required.")

        self.credentials = credentials
        self.config = config
        self.url = self.credentials.url.rstrip("/")
        self.verify_ssl = True
        self.timeout = self.config.file_processing.default_timeout
        self._session: Optional[requests.Session] = None

        # Set auth method based on credentials
        if self.credentials.pat_token:
            self.auth_method = "bearer"
        else:
            self.auth_method = "basic"

        self._initialize_confluence_client()
        self._configure_session()

    # -------------------------------
    # Context Management
    # -------------------------------
    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()

    def close(self):
        if self._session:
            self._session.close()
            self._session = None
            logger.debug("Custom requests session closed.")
        if hasattr(self.confluence, '_session') and self.confluence._session:
            self.confluence._session.close()
            logger.debug("Atlassian library session closed.")

        # Clear the module-level cache
        _cached_fetch_attachment.cache_clear()
        logger.debug("Attachment metadata cache cleared.")

    # -------------------------------
    # Internal Setup
    # -------------------------------
    def _initialize_confluence_client(self) -> None:
        kwargs = dict(
            url=self.url,
            timeout=self.timeout,
            verify_ssl=self.verify_ssl,
            cloud=self.credentials.cloud,
        )
        logger.debug(f"Initializing Confluence client with URL: {self.url}, cloud: {self.credentials.cloud}")

        if self.credentials.pat_token:
            logger.debug("Using PAT token authentication")
            self.confluence = Confluence(token=self.credentials.pat_token, **kwargs)
        elif self.credentials.username and self.credentials.api_token:
            logger.debug(f"Using username/API token authentication for user: {self.credentials.username}")
            self.confluence = Confluence(
                username=self.credentials.username,
                password=self.credentials.api_token,
                **kwargs
            )
        else:
            raise ConfluenceAuthenticationError(
                "No valid authentication method (PAT or user/token) found in credentials."
            )

        logger.debug("Confluence client initialized successfully")

    def _configure_session(self) -> None:
        if self._session is None:
            self._session = requests.Session()
        self._session.verify = self.verify_ssl
        headers = {
            "Accept": "application/json",
            "User-Agent": "Mozilla/5.0 (compatible; KbotConfluenceClient/1.3)"
        }
        headers.update(self.credentials.get_auth_headers())
        self._session.headers.update(headers)

    # -------------------------------
    # Low-level Networking
    # -------------------------------
    @contextmanager
    def _safe_request_context(self, method: str, url: str, **kwargs):
        response = None
        try:
            kwargs.setdefault('timeout', self.timeout)
            response = self._make_authenticated_request(method, url, **kwargs)
            response.raise_for_status()
            yield response
        except requests.exceptions.HTTPError as e:
            raise ConfluenceClientException.from_http_error(
                e.response,
                message=f"Confluence API error for {url}",
                original_exception=e
            ) from e
        except requests.exceptions.Timeout as e:
            raise ConfluenceTimeoutError(
                f"Request to {url} timed out",
                original_exception=e,
                resource=url
            ) from e
        except requests.exceptions.SSLError as e:
            raise ConfluenceClientException(
                f"SSL error during request to {url}: {e}",
                original_exception=e,
                resource=url,
                is_retryable=False
            ) from e
        except requests.exceptions.ConnectionError as e:
            raise ConfluenceClientException(
                f"Connection error during request to {url}: {e}",
                original_exception=e,
                resource=url,
                is_retryable=True
            ) from e
        except requests.exceptions.RequestException as e:
            raise ConfluenceClientException(
                f"Network error during request to {url}: {e}",
                original_exception=e,
                resource=url,
                is_retryable=False  # Conservative: unknown subclasses
            ) from e
        finally:
            if response:
                response.close()

    def _make_authenticated_request(self, method: str, url: str, **kwargs) -> requests.Response:
        if self._session is None:
            self._configure_session()
        return self._session.request(method, url, **kwargs)

    # -------------------------------
    # Attachments
    # -------------------------------
    def _is_valid_attachment(self, attachment: dict) -> bool:
        from ..config.confluence_helpers import is_temp_file
        title = attachment.get("title", "")
        if self.config and is_temp_file(self.config, title):
            return False
        if title.startswith((".", "~")):
            return False
        return True

    def _filter_current_attachments(self, all_attachments: List[dict]) -> List[dict]:
        return [
            att for att in all_attachments
            if att.get("status") == "current" and self._is_valid_attachment(att)
        ]

    def get_page_attachments_all_current(self, page_id: str, start: int = 0, limit: int = 50) -> List[dict]:
        retryer = create_search_retryer(self.config)
        try:
            def _get_attachments_call():
                return self.confluence.get_attachments_from_content(
                    page_id=page_id,
                    start=start,
                    limit=limit
                )

            response = retryer(_get_attachments_call)
            return self._filter_current_attachments(response.get("results", []))
        except Exception as e:
            if not isinstance(e, ConfluenceClientException):
                raise ConfluenceClientException(
                    f"Failed to retrieve attachments for page {page_id}",
                    original_exception=e,
                    resource=page_id
                ) from e
            raise

    def get_page_referenced_attachments(self, page_id: str) -> List[dict]:
        try:
            logger.debug("Fetching page content & attachments in one call.")
            page_data = self.get_page_content(
                page_id,
                expand="body.storage,children.attachment"
            ) or {}
            content_html = page_data.get('body', {}).get('storage', {}).get('value', '')
            attachment_results = page_data.get('children', {}).get('attachment', {}).get('results', [])

            current_attachments = self._filter_current_attachments(attachment_results)

            if not current_attachments:
                logger.debug("No current attachments for page %s.", page_id)
                return []

            max_size = self.config.file_processing.max_content_size_for_filtering
            if len(content_html.encode('utf-8')) > max_size:
                logger.warning(
                    "Page %s too large; skipping attachment filtering for safety.",
                    page_id
                )
                return []

            return self._extract_attachments_from_xml(content_html, current_attachments)

        except ConfluenceClientException:
            raise
        except Exception as e:
            raise ConfluenceClientException(
                f"Failed to process attachments for page {page_id}",
                original_exception=e,
                resource=page_id
            ) from e

    # -------------------------------
    # XML Parsing Helpers
    # -------------------------------
    def _extract_attachments_from_xml(self, content: str, attachments: List[dict]) -> List[dict]:
        """
        Parse Confluence storage XML to find explicitly referenced attachments.
        Uses fixed prefixes for speed & reliability.
        """
        # Fixed namespace map identical to original constants
        NS = {
            'ri': 'http://www.atlassian.com/schema/confluence/4/ri/',
            'ac': 'http://www.atlassian.com/schema/confluence/4/ac/'
        }

        try:
            stripped = content.strip()
            if not stripped.startswith(('<?xml', '<html')):
                stripped = f"""<root xmlns:ac="{NS['ac']}" xmlns:ri="{NS['ri']}">{stripped}</root>"""
            root = ET.fromstring(stripped)

            referenced: Set[str] = set()

            # 1️⃣ Draw.io macros
            for macro in root.findall(".//ac:structured-macro[@ac:name='drawio']", NS):
                for attr in ('diagramName', 'filename'):
                    param = macro.find(f".//ac:parameter[@ac:name='{attr}']", NS)
                    if param is not None and param.text:
                        referenced.add(param.text.strip())

            # 2️⃣ Standard ri:attachment tags
            for ri_att in root.findall(".//ri:attachment", NS):
                filename = ri_att.get(f"{{{NS['ri']}}}filename")
                if filename:
                    referenced.add(filename.strip())

            # 3️⃣ Catch-all filename in any attribute name
            for elem in root.iter():
                for attr_name, attr_value in elem.attrib.items():
                    if 'filename' in str(attr_name).lower():
                        referenced.add(attr_value.strip())

            if not referenced:
                return []

            lower_refs = {f.lower() for f in referenced}
            return [att for att in attachments
                    if att.get('title', '').lower() in lower_refs]

        except ET.ParseError as e:
            logger.warning("XML parsing failed for attachment filtering: %s", e)
            return []
        except Exception as e:
            logger.exception("Unexpected XML error: %s", e)
            return []

    # -------------------------------
    # Misc Public Methods
    # -------------------------------
    def _fetch_attachment_by_id_from_api(self, attachment_id: str) -> Dict:
        return _cached_fetch_attachment(self.url, attachment_id, self.verify_ssl)

    def get_attachment_by_id(self, attachment_id: str) -> Dict:
        return self._fetch_attachment_by_id_from_api(attachment_id)

    def get_attachment_content(self, attachment_id: str) -> bytes:
        retryer = create_api_retryer(self.config)
        try:
            meta = self.get_attachment_by_id(attachment_id)
            download_link = meta.get('_links', {}).get('download')
            if not download_link:
                raise ConfluenceClientException(
                    f"No download link for attachment {attachment_id}",
                    resource=attachment_id
                )
            download_url = self.url + download_link if download_link.startswith('/') else download_link
            return retryer(self._download_content_from_url, url=download_url)
        except Exception as e:
            if not isinstance(e, ConfluenceClientException):
                raise ConfluenceClientException(
                    f"Unexpected error retrieving attachment {attachment_id}",
                    original_exception=e,
                    resource=attachment_id
                ) from e
            raise

    def _download_content_from_url(self, url: str) -> bytes:
        with self._safe_request_context('GET', url) as resp:
            return resp.content

    def get_page_content(self, page_id: str,
                         expand: str = "body.storage,version,ancestors,children.attachment") -> Dict:
        retryer = create_api_retryer(self.config)
        try:
            def _get_page_call():
                return self.confluence.get_page_by_id(page_id=page_id, expand=expand)

            page = retryer(_get_page_call)
            if page is None:
                raise ConfluenceNotFoundError(
                    f"Confluence page with ID '{page_id}' was not found.",
                    resource=page_id
                )
            return page
        except Exception as e:
            if not isinstance(e, (ConfluenceClientException, ConfluenceNotFoundError)):
                raise ConfluenceClientException(
                    f"Failed to retrieve page {page_id}",
                    original_exception=e,
                    resource=page_id
                ) from e
            raise

    def search_content(self, cql: str, limit: int = 100, start: int = 0,
                       expand: str = "version,ancestors,children.attachment") -> List[dict]:
        retryer = create_search_retryer(self.config)
        try:
            logger.debug(f"Executing CQL search: '{cql}' (start={start}, limit={limit})")

            def _cql_call():
                try:
                    result = self.confluence.cql(cql=cql, start=start, limit=limit, expand=expand)
                    logger.debug(f"CQL search successful, returned {len(result.get('results', []))} results")
                    return result
                except Exception as inner_e:
                    logger.error(f"Inner CQL call failed: {type(inner_e).__name__}: {inner_e}")
                    # Log more details about the atlassian library error
                    if hasattr(inner_e, 'response') and inner_e.response:
                        logger.error(f"HTTP status: {getattr(inner_e.response, 'status_code', 'unknown')}")
                        logger.error(f"Response content: {getattr(inner_e.response, 'text', 'no content')[:500]}")
                    raise

            response = retryer(_cql_call)
            return response.get("results", [])
        except Exception as e:
            # Add more context about what went wrong
            error_context = {
                "cql": cql,
                "start": start,
                "limit": limit,
                "expand": expand,
                "confluence_url": self.url
            }

            if not isinstance(e, ConfluenceClientException):
                raise ConfluenceClientException(
                    f"CQL search failed: '{cql}' - {type(e).__name__}: {str(e)}",
                    original_exception=e,
                    context=error_context
                ) from e
            raise

    def export_page_as_pdf(self, page_id: str) -> bytes:
        retryer = create_api_retryer(self.config)
        try:
            logger.info("Exporting page %s as PDF", page_id)

            def _pdf_export_call():
                return self.confluence.get_page_as_pdf(page_id=page_id)

            pdf_content = retryer(_pdf_export_call)
            if pdf_content is None:
                raise ConfluenceClientException(
                    f"PDF export returned None for page {page_id}",
                    resource=page_id,
                    operation="export_page_as_pdf"
                )
            logger.info("PDF export successful for page %s", page_id)
            return pdf_content
        except Exception as e:
            if not isinstance(e, ConfluenceClientException):
                logger.error("Failed to export page %s as PDF: %s", page_id, e, exc_info=True)
                raise ConfluenceClientException(
                    f"Failed to export page {page_id} as PDF: {e}",
                    original_exception=e,
                    resource=page_id,
                    operation="export_page_as_pdf"
                ) from e
            raise
